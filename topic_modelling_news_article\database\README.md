# 🗄️ Database Setup for Topic Modeling Pipeline

## 📁 Directory Structure

```
database/
├── schema.sql                    # Core MySQL schema (tables only)
├── powerbi_views.sql            # Power BI DirectQuery views
├── mysql_complete_setup.sql     # Complete setup (schema + views + data)
├── sql_connection.py            # MySQL connection manager
├── sql_exporter.py              # Data export utilities
├── test_connection.py           # Connection test script
└── README.md                    # This file
```

## 🚀 Quick Setup (Choose One Method)

### Method 1: Complete Setup (Recommended)
**Run everything in one script:**
```sql
-- In MySQL Workbench, run:
database/mysql_complete_setup.sql
```
This creates:
- ✅ Database and all 4 tables
- ✅ Sample topics data (8 topics)
- ✅ All 6 Power BI views
- ✅ Foreign key relationships
- ✅ Verification queries

### Method 2: Step-by-Step Setup
```sql
-- Step 1: Create tables and data
database/schema.sql

-- Step 2: Create Power BI views
database/powerbi_views.sql
```

## 🔧 Configuration

### 1. Database Connection
Edit `config/database.json`:
```json
{
  "server": "localhost",
  "database": "TopicModelingDB",
  "username": "your_mysql_username",
  "password": "your_mysql_password",
  "port": 3306,
  "driver": "mysql+pymysql"
}
```

### 2. Install Dependencies
```bash
pip install pymysql mysql-connector-python sqlalchemy
```

## 🧪 Testing Your Setup

### Test Database Connection
```bash
# From project root
cd database
python test_connection.py

# Or show current config
python test_connection.py --config
```

### Expected Test Results
```
✅ Configuration file found
✅ Connection manager created successfully
✅ Basic connection test passed
✅ Connected to database: TopicModelingDB
✅ Table 'raw_articles' exists
✅ Table 'preprocessed_articles' exists
✅ Table 'topics' exists
✅ Table 'topic_results' exists
✅ Topics table has 8 topics
✅ All Power BI views exist and queryable
✅ Foreign key relationships verified
```

## 📊 Database Schema

### Core Tables (Connected Workflow)
```
raw_articles (NewsAPI data)
    ↓ (article_id)
preprocessed_articles (cleaned text)
    ↓ (preprocessed_id + article_id)
topic_results (LDA classifications)
    ↓ (topic_id)
topics (topic definitions)
```

### Power BI Views
1. **vw_topic_summary** - Main dashboard overview
2. **vw_kpi_metrics** - Key performance indicators
3. **vw_hourly_trends** - Real-time trend analysis
4. **vw_realtime_articles** - Latest articles feed
5. **vw_source_analysis** - Source diversity metrics
6. **vw_pipeline_health** - Pipeline monitoring

## 🔗 Foreign Key Relationships
- `preprocessed_articles.article_id` → `raw_articles.article_id`
- `topic_results.article_id` → `raw_articles.article_id`
- `topic_results.preprocessed_id` → `preprocessed_articles.preprocessed_id`
- `topic_results.topic_id` → `topics.topic_id`

## 🚨 Troubleshooting

### Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Check if database exists
mysql -u root -p -e "SHOW DATABASES LIKE 'TopicModelingDB';"

# Test connection manually
mysql -u your_username -p TopicModelingDB
```

### Common Errors
1. **"Access denied"** → Check username/password in config
2. **"Database doesn't exist"** → Run mysql_complete_setup.sql
3. **"Table doesn't exist"** → Run schema.sql
4. **"View doesn't exist"** → Run powerbi_views.sql
5. **"Module not found"** → Install: `pip install pymysql`

## 📈 Performance Optimization

### Indexes Created
- `IX_published_timestamp` - For time-based queries
- `IX_fetched_at` - For pipeline status tracking
- `IX_content_hash` - For deduplication
- `IX_preprocessing_timestamp` - For processing order
- `IX_classification_timestamp` - For Power BI DirectQuery

### Connection Pooling
- Pool size: 10 connections
- Max overflow: 20 connections
- Pool timeout: 30 seconds
- Pre-ping validation enabled

## 🎯 Next Steps

After successful database setup:

1. **Configure NewsAPI**: Set `NEWS_API_KEY` environment variable
2. **Add LDA Model**: Place your `.pkl` file in `models/` directory
3. **Test Pipeline**: Run `python automated_pipeline.py --single-run`
4. **Setup Power BI**: Connect to MySQL using DirectQuery
5. **Schedule Pipeline**: Use `scheduler/` for automated runs

## 📝 File Descriptions

- **schema.sql**: Core database structure only
- **powerbi_views.sql**: Views optimized for Power BI DirectQuery
- **mysql_complete_setup.sql**: Everything combined for convenience
- **sql_connection.py**: Python MySQL connection manager
- **sql_exporter.py**: Utilities for data export
- **test_connection.py**: Comprehensive connection testing

Your MySQL database is now ready for the complete topic modeling pipeline! 🎉
