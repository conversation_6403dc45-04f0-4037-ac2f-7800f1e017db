{"_comment": "MySQL Database Configuration for Topic Modeling Pipeline", "_description": "Configure your MySQL connection settings below", "server": "localhost", "database": "TopicModelingDB", "username": "your_mysql_username", "password": "your_mysql_password", "port": 3306, "driver": "mysql+pymysql", "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "_examples": {"local_mysql": {"server": "localhost", "database": "TopicModelingDB", "username": "root", "password": "your_password", "port": 3306}, "remote_mysql": {"server": "your-mysql-server.com", "database": "TopicModelingDB", "username": "your_username", "password": "your_password", "port": 3306}, "mysql_with_custom_port": {"server": "localhost", "database": "TopicModelingDB", "username": "your_username", "password": "your_password", "port": 3307}}, "_notes": ["Make sure MySQL server is running and accessible", "Install required Python packages: pip install pymysql mysql-connector-python", "Create the database first: CREATE DATABASE TopicModelingDB;", "Default MySQL port is 3306, change if using custom port", "Pool settings control connection pooling for better performance"]}