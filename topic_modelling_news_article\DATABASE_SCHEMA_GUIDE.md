# 🗄️ Complete Database Schema Guide

## 📊 **Schema Overview**

The database follows a **normalized design** optimized for real-time topic modeling with these core principles:
- **Data integrity** through foreign keys
- **Performance** through strategic indexing
- **Scalability** for millions of articles
- **Power BI optimization** through specialized views

## 🏗️ **Database Architecture**

```
TopicModelingDB
├── 📰 raw_articles          (NewsAPI responses)
├── 🔤 preprocessed_articles (Cleaned text)
├── 📋 topics               (Topic definitions)
├── 🎯 topic_results        (LDA classifications)
└── 📊 Power BI Views       (Dashboard optimization)
```

## 📋 **Core Tables Detailed**

### **1. raw_articles** - NewsAPI Data Storage
```sql
CREATE TABLE raw_articles (
    article_id VARCHAR(50) PRIMARY KEY,      -- MD5 hash of URL + timestamp
    title VARCHAR(500) NOT NULL,             -- Article headline
    description TEXT,                        -- Article summary
    content LONGTEXT,                        -- Full article content
    source_name VARCHAR(100) NOT NULL,       -- News source (BBC, CNN, etc.)
    author <PERSON><PERSON><PERSON><PERSON>(200),                     -- Article author
    url VARCHAR(1000) UNIQUE,                -- Original article URL
    published_at DATETIME NOT NULL,          -- When article was published
    fetched_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- When we retrieved it
    content_hash VARCHAR(64) UNIQUE,         -- SHA256 for deduplication
    raw_json LONGTEXT,                       -- Complete NewsAPI response
    processing_status VARCHAR(20) DEFAULT 'pending' -- pending/processed/failed
);
```

**Purpose:** Store original NewsAPI responses with deduplication
**Volume:** ~2,400 articles/day, ~876,000/year
**Key Features:**
- Content hash prevents duplicates
- Processing status tracks pipeline progress
- Raw JSON preserves all original data

### **2. preprocessed_articles** - Cleaned Text
```sql
CREATE TABLE preprocessed_articles (
    preprocessed_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- Unique processing ID
    article_id VARCHAR(50) NOT NULL,                   -- Links to raw_articles
    cleaned_text LONGTEXT NOT NULL,                    -- Processed text
    tokens LONGTEXT,                                   -- Tokenized words
    lemmatized_text LONGTEXT,                          -- Lemmatized text
    word_count INT,                                    -- Number of words
    preprocessing_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    preprocessing_duration_ms INT,                     -- Processing time
    
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    UNIQUE KEY UQ_article_preprocessing (article_id)  -- One preprocessing per article
);
```

**Purpose:** Store NLP-processed text ready for topic modeling
**Processing:** NLTK/spaCy cleaning, tokenization, lemmatization
**Relationship:** 1:1 with raw_articles

### **3. topics** - Topic Definitions
```sql
CREATE TABLE topics (
    topic_id INT PRIMARY KEY,                -- Topic number (0, 1, 2, ...)
    topic_label VARCHAR(100) NOT NULL,       -- Human-readable name
    topic_keywords LONGTEXT,                 -- JSON array of keywords
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**Purpose:** Define topic labels and keywords from your LDA model
**Sample Data:**
- Topic 0: Technology ["technology", "tech", "innovation", "ai"]
- Topic 1: Politics ["politics", "government", "election", "policy"]
- Topic 2: Business ["business", "economy", "market", "financial"]

### **4. topic_results** - LDA Classifications
```sql
CREATE TABLE topic_results (
    result_id BIGINT AUTO_INCREMENT PRIMARY KEY,      -- Unique result ID
    article_id VARCHAR(50) NOT NULL,                  -- Links to raw_articles
    preprocessed_id BIGINT NOT NULL,                  -- Links to preprocessed_articles
    topic_id INT NOT NULL,                            -- Assigned topic
    confidence DECIMAL(8,6) NOT NULL,                 -- LDA confidence score
    topic_distribution LONGTEXT,                      -- JSON of all topic probabilities
    topic_keywords LONGTEXT,                          -- Keywords for this classification
    classification_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    model_version VARCHAR(20) DEFAULT '1.0',          -- LDA model version
    processing_duration_ms INT,                       -- Classification time
    
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (preprocessed_id) REFERENCES preprocessed_articles(preprocessed_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id)
);
```

**Purpose:** Store LDA model predictions and confidence scores
**Volume:** Same as articles (~2,400/day)
**Key Data:** Topic assignment, confidence, full probability distribution

## 🔗 **Relationships & Data Flow**

```
raw_articles (1) ←→ (1) preprocessed_articles
     ↓                        ↓
     └─────→ topic_results ←───┘
                  ↓
              topics (lookup)
```

**Data Flow:**
1. **NewsAPI** → `raw_articles` (JSON storage)
2. **NLP Processing** → `preprocessed_articles` (cleaned text)
3. **LDA Model** → `topic_results` (classifications)
4. **Power BI** → Views (dashboard data)

## 📊 **Power BI Views**

### **Main Dashboard Views:**
- `vw_topic_summary` - Topic overview with article counts and confidence
- `vw_kpi_metrics` - Key performance indicators
- `vw_daily_trends_enhanced` - Daily topic trends
- `vw_hourly_trends_enhanced` - Real-time hourly patterns
- `vw_recent_articles` - Latest classified articles
- `vw_pipeline_health` - System monitoring

### **Example View - Topic Summary:**
```sql
CREATE VIEW vw_topic_summary AS
SELECT 
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as total_articles,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    MAX(tr.classification_timestamp) as last_updated
FROM topics t
LEFT JOIN topic_results tr ON t.topic_id = tr.topic_id
LEFT JOIN raw_articles ra ON tr.article_id = ra.article_id
GROUP BY t.topic_id, t.topic_label;
```

## 🔍 **How to Explore the Schema**

### **1. View Schema Files**
```bash
# Core schema
cat database/schema.sql

# Power BI views
cat database/powerbi_views.sql

# Complete setup
cat database/mysql_complete_setup.sql
```

### **2. MySQL Workbench Exploration**
```sql
-- Connect to database
USE TopicModelingDB;

-- Show all tables
SHOW TABLES;

-- Describe table structure
DESCRIBE raw_articles;
DESCRIBE preprocessed_articles;
DESCRIBE topic_results;
DESCRIBE topics;

-- Show indexes
SHOW INDEX FROM raw_articles;

-- Show views
SHOW FULL TABLES WHERE Table_type = 'VIEW';
```

### **3. Python Schema Inspection**
```python
# Run this to explore schema programmatically
python database/test_connection.py

# Or use this script:
from database.sql_connection import SQLConnectionManager

sql_manager = SQLConnectionManager.from_config('config/database.json')
with sql_manager.get_connection() as conn:
    # Get table info
    tables = conn.execute("SHOW TABLES").fetchall()
    print("Tables:", [t[0] for t in tables])
    
    # Get column info
    columns = conn.execute("DESCRIBE raw_articles").fetchall()
    for col in columns:
        print(f"Column: {col[0]}, Type: {col[1]}")
```

### **4. Sample Data Queries**
```sql
-- Check sample data
SELECT COUNT(*) FROM raw_articles;
SELECT COUNT(*) FROM preprocessed_articles;
SELECT COUNT(*) FROM topic_results;

-- View topic distribution
SELECT topic_label, COUNT(*) as article_count 
FROM vw_topic_summary 
ORDER BY article_count DESC;

-- Recent activity
SELECT * FROM raw_articles 
ORDER BY fetched_at DESC 
LIMIT 5;
```

## 📈 **Performance Features**

### **Strategic Indexing:**
```sql
-- Time-based queries (most common)
INDEX IX_published_timestamp (published_at DESC, processing_status)
INDEX IX_classification_timestamp (classification_timestamp DESC, topic_id, confidence DESC)

-- Deduplication
INDEX IX_content_hash (content_hash)

-- Foreign key performance
INDEX IX_preprocessing_timestamp (preprocessing_timestamp DESC)
```

### **Data Types Optimization:**
- `VARCHAR(50)` for IDs - Optimal for MD5 hashes
- `DECIMAL(8,6)` for confidence - High precision for LDA scores
- `LONGTEXT` for content - Handles full articles up to 4GB
- `DATETIME` throughout - Consistent time tracking

## 🛠️ **Schema Maintenance**

### **Regular Maintenance Queries:**
```sql
-- Check data freshness
SELECT MAX(fetched_at) as last_fetch FROM raw_articles;

-- Monitor processing status
SELECT processing_status, COUNT(*) 
FROM raw_articles 
GROUP BY processing_status;

-- Check topic distribution
SELECT topic_label, COUNT(*) as articles
FROM vw_topic_summary 
ORDER BY articles DESC;

-- Performance monitoring
SELECT AVG(preprocessing_duration_ms) as avg_preprocessing_time,
       AVG(processing_duration_ms) as avg_classification_time
FROM preprocessed_articles pa
JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id;
```

This schema is designed for **production scale** with millions of articles, **real-time processing**, and **Power BI optimization**!
