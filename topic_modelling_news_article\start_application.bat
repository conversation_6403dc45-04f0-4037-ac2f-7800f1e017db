@echo off
REM =====================================================
REM Topic Modeling Pipeline - Complete Application Startup
REM =====================================================

echo.
echo ========================================================
echo 🚀 Topic Modeling Pipeline - Application Startup
echo ========================================================
echo.

REM Set working directory to script location
cd /d "%~dp0"

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo 💡 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found: 
python --version

REM Step 1: Install Dependencies
echo.
echo 📦 Step 1: Installing Dependencies...
echo ----------------------------------------
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Step 2: Download NLTK Data
echo.
echo 📚 Step 2: Downloading NLTK Data...
echo ----------------------------------------
python -c "import nltk; nltk.download('punkt', quiet=True); nltk.download('stopwords', quiet=True); nltk.download('wordnet', quiet=True); print('✅ NLTK data downloaded')"

REM Step 3: Download spaCy Model
echo.
echo 🧠 Step 3: Downloading spaCy English Model...
echo ----------------------------------------
python -m spacy download en_core_web_sm
if errorlevel 1 (
    echo ⚠️ spaCy model download failed - continuing anyway
)

REM Step 4: Check Configuration
echo.
echo ⚙️ Step 4: Checking Configuration...
echo ----------------------------------------

REM Check if database config exists
if not exist "config\database.json" (
    echo ⚠️ Database configuration not found
    echo 💡 Copying example configuration...
    copy "config\database.json.example" "config\database.json"
    echo.
    echo ❗ IMPORTANT: Please edit config\database.json with your MySQL credentials
    echo   - Update username and password
    echo   - Verify server and database name
    echo.
    pause
)

REM Check if .env file exists
if not exist "config\.env" (
    echo ⚠️ Environment configuration not found
    echo 💡 Creating .env file...
    echo # News API Configuration > config\.env
    echo NEWS_API_KEY=your_newsapi_key_here >> config\.env
    echo. >> config\.env
    echo # MySQL Configuration >> config\.env
    echo MYSQL_SERVER=localhost >> config\.env
    echo MYSQL_DATABASE=TopicModelingDB >> config\.env
    echo MYSQL_USERNAME=your_mysql_username >> config\.env
    echo MYSQL_PASSWORD=your_mysql_password >> config\.env
    echo MYSQL_PORT=3306 >> config\.env
    echo.
    echo ❗ IMPORTANT: Please edit config\.env with your API key and database credentials
    echo.
    pause
)

REM Step 5: Check MySQL Connection
echo.
echo 🔗 Step 5: Testing MySQL Connection...
echo ----------------------------------------
cd database
python test_connection.py
if errorlevel 1 (
    echo.
    echo ❌ MySQL connection failed!
    echo 💡 Please check:
    echo   1. MySQL server is running
    echo   2. Database credentials in config\database.json are correct
    echo   3. TopicModelingDB database exists
    echo.
    echo 🛠️ To create the database, run:
    echo   mysql -u root -p -e "CREATE DATABASE TopicModelingDB;"
    echo   mysql -u root -p TopicModelingDB ^< mysql_complete_setup.sql
    echo.
    pause
    cd ..
    exit /b 1
)
cd ..

REM Step 6: Check for LDA Model
echo.
echo 🧠 Step 6: Checking LDA Model...
echo ----------------------------------------
if not exist "models\lda_model.pkl" (
    echo ⚠️ LDA model not found at models\lda_model.pkl
    echo 💡 Please place your trained LDA model in the models\ directory
    echo.
    pause
)

REM Step 7: Run Setup Validation
echo.
echo ✅ Step 7: Running Setup Validation...
echo ----------------------------------------
python setup.py
if errorlevel 1 (
    echo ❌ Setup validation failed
    pause
    exit /b 1
)

REM Step 8: Start Application Menu
:menu
echo.
echo ========================================================
echo 🎯 Application Ready! Choose an option:
echo ========================================================
echo.
echo 1. Run Single Pipeline Cycle (Test)
echo 2. Start Continuous Pipeline
echo 3. Check Pipeline Status
echo 4. Monitor Pipeline Health
echo 5. Test Database Connection
echo 6. View Logs
echo 7. Open MySQL Workbench Setup Guide
echo 8. Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto single_run
if "%choice%"=="2" goto continuous_run
if "%choice%"=="3" goto status_check
if "%choice%"=="4" goto health_monitor
if "%choice%"=="5" goto test_connection
if "%choice%"=="6" goto view_logs
if "%choice%"=="7" goto mysql_guide
if "%choice%"=="8" goto exit
echo Invalid choice. Please try again.
goto menu

:single_run
echo.
echo 🔄 Running Single Pipeline Cycle...
echo ----------------------------------------
python automated_pipeline.py --single-run
pause
goto menu

:continuous_run
echo.
echo 🔄 Starting Continuous Pipeline...
echo ----------------------------------------
echo Press Ctrl+C to stop the pipeline
python automated_pipeline.py --continuous
pause
goto menu

:status_check
echo.
echo 📊 Checking Pipeline Status...
echo ----------------------------------------
python automated_pipeline.py --status
pause
goto menu

:health_monitor
echo.
echo 🏥 Monitoring Pipeline Health...
echo ----------------------------------------
python monitor_pipeline.py check
pause
goto menu

:test_connection
echo.
echo 🔗 Testing Database Connection...
echo ----------------------------------------
cd database
python test_connection.py
cd ..
pause
goto menu

:view_logs
echo.
echo 📋 Recent Log Entries...
echo ----------------------------------------
if exist "logs\automated_pipeline.log" (
    echo Last 20 lines from automated_pipeline.log:
    echo ----------------------------------------
    powershell "Get-Content logs\automated_pipeline.log -Tail 20"
) else (
    echo No log file found yet.
)
pause
goto menu

:mysql_guide
echo.
echo 📖 Opening MySQL Workbench Setup Guide...
echo ----------------------------------------
echo.
echo MySQL Workbench Setup Instructions:
echo.
echo 1. Open MySQL Workbench
echo 2. Create a new connection:
echo    - Connection Name: TopicModeling
echo    - Hostname: localhost
echo    - Port: 3306
echo    - Username: [your username]
echo 3. Test the connection
echo 4. Create database: CREATE DATABASE TopicModelingDB;
echo 5. Run the schema: database\mysql_complete_setup.sql
echo.
echo For detailed instructions, see: database\README.md
echo.
pause
goto menu

:exit
echo.
echo 👋 Goodbye! Pipeline stopped.
echo.
pause
exit /b 0
