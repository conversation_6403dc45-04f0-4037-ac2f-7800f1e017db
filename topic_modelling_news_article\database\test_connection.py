#!/usr/bin/env python3
"""
Test MySQL Connection for Topic Modeling Pipeline
Verifies database connectivity and table structure
"""

import sys
import os
from pathlib import Path

try:
    from sql_connection import SQLConnectionManager
    print("✅ SQL connection module imported successfully")
except ImportError as e:
    print(f"❌ Failed to import SQL connection module: {e}")
    sys.exit(1)

def test_mysql_connection():
    """Test MySQL database connection and verify setup."""
    
    print("🔍 Testing MySQL Connection for Topic Modeling Pipeline")
    print("=" * 60)
    
    # Step 1: Test configuration file
    print("\n📋 Step 1: Checking Configuration")
    print("-" * 35)
    
    config_file = Path("../config/database.json")
    if not config_file.exists():
        print("❌ Configuration file not found: config/database.json")
        print("💡 Copy config/database.json.example to config/database.json and edit it")
        return False
    
    print("✅ Configuration file found")
    
    # Step 2: Test database connection
    print("\n🔗 Step 2: Testing Database Connection")
    print("-" * 40)
    
    try:
        # Create connection manager
        sql_manager = SQLConnectionManager.from_config_file('../config/database.json')
        print("✅ Connection manager created successfully")
        
        # Test basic connection
        with sql_manager.get_connection() as conn:
            result = conn.execute("SELECT 1 as test").fetchone()
            if result:
                print("✅ Basic connection test passed")
            else:
                print("❌ Basic connection test failed")
                return False
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\n💡 Troubleshooting tips:")
        print("   1. Make sure MySQL server is running")
        print("   2. Check username/password in config/database.json")
        print("   3. Verify database 'TopicModelingDB' exists")
        print("   4. Install required packages: pip install pymysql mysql-connector-python")
        return False
    
    # Step 3: Verify database structure
    print("\n🗄️ Step 3: Verifying Database Structure")
    print("-" * 45)
    
    try:
        with sql_manager.get_connection() as conn:
            # Check if database exists and is selected
            result = conn.execute("SELECT DATABASE() as current_db").fetchone()
            print(f"✅ Connected to database: {result.current_db}")
            
            # Check required tables
            required_tables = ['raw_articles', 'preprocessed_articles', 'topics', 'topic_results']
            
            for table in required_tables:
                result = conn.execute(f"SHOW TABLES LIKE '{table}'").fetchone()
                if result:
                    print(f"✅ Table '{table}' exists")
                else:
                    print(f"❌ Table '{table}' missing")
                    print(f"💡 Run mysql_complete_setup.sql in MySQL Workbench to create tables")
                    return False
            
            # Check topics data
            result = conn.execute("SELECT COUNT(*) as count FROM topics").fetchone()
            if result and result.count > 0:
                print(f"✅ Topics table has {result.count} topics")
            else:
                print("❌ Topics table is empty")
                print("💡 Run mysql_complete_setup.sql to insert sample topics")
                return False
                
    except Exception as e:
        print(f"❌ Database structure verification failed: {e}")
        return False
    
    # Step 4: Test Power BI views
    print("\n📊 Step 4: Testing Power BI Views")
    print("-" * 35)
    
    try:
        with sql_manager.get_connection() as conn:
            # Check required views
            required_views = ['vw_topic_summary', 'vw_kpi_metrics', 'vw_hourly_trends', 
                            'vw_realtime_articles', 'vw_pipeline_health']
            
            for view in required_views:
                result = conn.execute(f"SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_TopicModelingDB = '{view}'").fetchone()
                if result:
                    print(f"✅ View '{view}' exists")
                else:
                    print(f"❌ View '{view}' missing")
                    print(f"💡 Run mysql_complete_setup.sql to create Power BI views")
                    return False
            
            # Test a view query
            result = conn.execute("SELECT COUNT(*) as count FROM vw_topic_summary").fetchone()
            print(f"✅ Power BI views are queryable (topic summary: {result.count} topics)")
                
    except Exception as e:
        print(f"❌ Power BI views test failed: {e}")
        return False
    
    # Step 5: Test workflow connectivity
    print("\n🔄 Step 5: Testing Workflow Connectivity")
    print("-" * 45)
    
    try:
        with sql_manager.get_connection() as conn:
            # Test foreign key relationships
            result = conn.execute("""
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_SCHEMA = 'TopicModelingDB'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            """).fetchall()
            
            if result:
                print("✅ Foreign key relationships verified:")
                for row in result:
                    print(f"   {row.TABLE_NAME}.{row.COLUMN_NAME} → {row.REFERENCED_TABLE_NAME}.{row.REFERENCED_COLUMN_NAME}")
            else:
                print("⚠️ No foreign key relationships found")
                
    except Exception as e:
        print(f"❌ Workflow connectivity test failed: {e}")
        return False
    
    # Step 6: Success summary
    print("\n🎉 Step 6: Connection Test Summary")
    print("-" * 40)
    
    print("✅ All tests passed! Your MySQL database is ready for:")
    print("   1. ✅ NewsAPI data ingestion → raw_articles table")
    print("   2. ✅ Text preprocessing → preprocessed_articles table")
    print("   3. ✅ LDA topic classification → topic_results table")
    print("   4. ✅ Power BI DirectQuery visualization")
    print("   5. ✅ Complete workflow connectivity")
    
    print("\n🚀 Next steps:")
    print("   1. Set your NewsAPI key: set NEWS_API_KEY=your_key")
    print("   2. Place your LDA model: models/lda_model.pkl")
    print("   3. Run the pipeline: python automated_pipeline.py --single-run")
    
    return True

def show_connection_info():
    """Show current connection configuration."""
    
    print("\n⚙️ Current MySQL Configuration:")
    print("-" * 35)
    
    try:
        import json
        with open('../config/database.json', 'r') as f:
            config = json.load(f)
        
        print(f"Server: {config.get('server', 'Not set')}")
        print(f"Database: {config.get('database', 'Not set')}")
        print(f"Username: {config.get('username', 'Not set')}")
        print(f"Port: {config.get('port', 'Not set')}")
        print(f"Driver: {config.get('driver', 'Not set')}")
        
    except Exception as e:
        print(f"❌ Could not read configuration: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test MySQL Connection for Topic Modeling')
    parser.add_argument('--config', action='store_true', help='Show current configuration')
    
    args = parser.parse_args()
    
    if args.config:
        show_connection_info()
    else:
        success = test_mysql_connection()
        if not success:
            print("\n❌ Connection test failed. Please check the errors above.")
            sys.exit(1)
        else:
            print("\n✅ MySQL connection test completed successfully!")
