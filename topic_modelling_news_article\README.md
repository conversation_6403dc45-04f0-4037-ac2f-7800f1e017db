# 🚀 **Real-Time Topic Modeling with MySQL & Power BI DirectQuery**

## 🧠 **Project Overview**

**Enterprise-Grade Topic Modeling Pipeline with MySQL Integration**

A production-ready data pipeline that fetches news articles, applies your trained LDA model for topic classification, stores results in MySQL, and provides real-time Power BI dashboards via DirectQuery - eliminating manual data refresh and CSV dependencies.

## 🏗️ **System Architecture**

```
+-------------------+    +-------------------+    +-------------------+    +-------------------+    +-------------------+
| 1. Data Ingestion |    | 2. Text Processing|    | 3. Topic Modeling |    | 4. MySQL Database |    | 5. Power BI       |
+-------------------+    +-------------------+    +-------------------+    +-------------------+    +-------------------+
| • NewsAPI/GNews   |--->| • Advanced cleaning|--->| • Your LDA Model  |--->| • Real-time insert|--->| • DirectQuery     |
| • RSS feeds       |    | • NLTK/spaCy       |    | • Classification  |    | • Aggregated views|    | • Live dashboards |
| • Deduplication   |    | • Lemmatization    |    | • Confidence score|    | • Optimized schema|    | • Zero refresh    |
| • Hourly schedule |    | • Batch processing |    | • Batch processing|    | • Performance idx |    | • Real-time data  |
+-------------------+    +-------------------+    +-------------------+    +-------------------+    +-------------------+
```

### **🔄 Data Flow**
```
NewsAPI → raw_articles → preprocessed_articles → topic_results → Power BI DirectQuery → Live Dashboard
```

### **🎯 Key Benefits**
- ✅ **Real-time dashboards** with Power BI DirectQuery
- ✅ **Zero manual refresh** - data updates automatically
- ✅ **Scalable MySQL** backend for enterprise use
- ✅ **Optimized performance** with pre-aggregated views
- ✅ **Production-ready** monitoring and error handling

## 📁 **Project Structure**

```
topic_modelling/
├── 📁 database/                     # 🗄️ MySQL Integration Layer
│   ├── schema.sql                  # Complete database schema (tables, indexes, procedures)
│   ├── powerbi_views.sql           # Optimized views for Power BI DirectQuery
│   ├── sql_connection.py           # High-performance connection manager
│   └── sql_exporter.py             # Real-time data export to MySQL
├── 📁 scripts/                      # ⚙️ Core Pipeline Components
│   ├── fetch_news.py               # Advanced NewsAPI data ingestion
│   ├── preprocess.py               # NLTK/spaCy text preprocessing
│   ├── topic_model.py              # LDA model integration service
│   └── run_pipeline.py             # Complete pipeline orchestrator
├── 📁 config/                       # ⚙️ Configuration Management
│   ├── database.json               # SQL Server connection configuration
│   ├── database.json.example       # Configuration template with examples
│   └── pipeline.yaml              # Pipeline settings and parameters
├── 📁 powerbi/                      # 📊 Power BI DirectQuery Integration
│   └── DIRECTQUERY_SETUP.md        # Complete Power BI DirectQuery setup guide
├── 📁 scheduler/                    # ⏰ Automation & Scheduling
│   ├── windows_task.xml            # Windows Task Scheduler configuration
│   └── crontab.txt                 # Linux/Mac cron job templates
├── 📁 models/                       # 🧠 Machine Learning Models
│   └── lda_model.pkl               # ← YOUR TRAINED LDA MODEL (place here)
├── 📁 data/                         # 💾 Temporary Data Storage
│   ├── raw_articles.csv            # Collected news articles (temporary)
│   └── processed_articles.csv      # Articles with topic classifications (temporary)
├── 📁 logs/                         # 📝 Logging & Monitoring
│   ├── pipeline.log                # Main pipeline execution logs
│   ├── monitor.log                 # Health monitoring logs
│   └── pipeline_status.json        # Current pipeline status
├── 📈 monitor_pipeline.py           # Comprehensive health monitoring system
├── ⚙️ setup.py                      # Setup validation and environment check
├── 📋 requirements.txt              # Python dependencies (includes SQL Server)
├── 📖 README.md                     # Complete project documentation
├── 📖 DEPLOYMENT_SUMMARY.md         # Deployment guide and summary
├── 📖 PROJECT_STATUS.md             # Project status and verification
└── 📖 DIRECTORY_STRUCTURE.md        # Detailed structure explanation
```

## 🧠 **LDA Model Setup**

### **Step 1: Place Your .pkl File**

Put your trained LDA model file in the `models/` directory:

```bash
models/
└── lda_model.pkl    # ← Your trained model here
```

### **Step 2: Model File Requirements**

Your `.pkl` file should contain a dictionary with these keys:

```python
{
    'model': trained_lda_model,           # Scikit-learn LDA model
    'vectorizer': count_vectorizer,       # Fitted CountVectorizer
    'topics': {                           # Topic information
        0: {
            'label': 'Technology_AI',
            'keywords': ['artificial', 'intelligence', 'technology', ...]
        },
        1: {
            'label': 'Politics_Election',
            'keywords': ['election', 'political', 'voting', ...]
        },
        # ... more topics
    },
    'method': 'sklearn'                   # Model type
}
```

### **Step 3: Alternative Model Names**

The system will look for models in this order:
1. `models/lda_model.pkl` (default)
2. `models/latest_model.pkl`
3. `models/topic_model_*.pkl` (most recent)

## ⚙️ **Technology Stack**

| Layer          | Technology                    |
| -------------- | ----------------------------- |
| Data Source    | NewsAPI / GNews / RSS         |
| Language       | Python 3.8+                  |
| NLP Tools      | NLTK, spaCy, BeautifulSoup    |
| Topic Modeling | Your trained LDA (.pkl)      |
| Visualization  | Power BI Desktop/Service      |
| Scheduler      | Windows Task Scheduler / cron |
| Storage        | CSV files / SQLite (optional)|
| Monitoring     | Custom health check system   |

## 🔄 **Complete Workflow**

### **Step 1: Data Ingestion** (`scripts/fetch_news.py`)
- Fetches articles from NewsAPI with advanced filtering
- Implements deduplication and data validation
- Supports RSS feeds as fallback
- Stores in CSV or SQLite database

### **Step 2: Text Preprocessing** (`scripts/preprocess.py`)
- Advanced HTML cleaning and URL removal
- NLTK/spaCy tokenization and lemmatization
- News-specific artifact removal
- Batch processing for efficiency

### **Step 3: Topic Classification** (`scripts/topic_model.py`)
- Loads your trained LDA model from `.pkl` file
- Applies preprocessing pipeline
- Classifies articles with confidence scoring
- Handles model validation and error recovery

### **Step 4: Power BI Export** (`scripts/export_to_csv.py`)
- Generates 6 comprehensive datasets
- Creates time-based trends and analytics
- Optimizes data for visualization
- Maintains data quality and consistency

### **Step 5: Pipeline Orchestration** (`scripts/run_pipeline.py`)
- Coordinates all pipeline steps
- Handles scheduling and automation
- Provides comprehensive logging
- Supports individual step execution

## 🚀 Quick Start

### **Prerequisites**
- ✅ Python 3.8+
- ✅ Microsoft SQL Server (local or remote)
- ✅ SQL Server ODBC Driver 17+
- ✅ Trained LDA model in `.pkl` format
- ✅ NewsAPI key (free from [newsapi.org](https://newsapi.org/))

### **1. SQL Server Setup**
```sql
-- Create database
CREATE DATABASE TopicModelingDB;
USE TopicModelingDB;

-- Run schema creation
-- Execute: database/schema.sql
-- Execute: database/powerbi_views.sql
```

### **2. Place Your Model**
```bash
# Put your trained LDA model here:
models/lda_model.pkl
```

### **3. Configuration**
```bash
# Install dependencies
pip install -r requirements.txt

# Configure API key
cp config/.env.example config/.env
# Edit config/.env with your NewsAPI key

# Configure SQL Server connection
cp config/database.json.example config/database.json
# Edit config/database.json with your SQL Server details

# Run setup validation
python setup.py

# Download required NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"

# Install spaCy English model
python -m spacy download en_core_web_sm
```

### **4. Run Complete Pipeline**
```bash
# Test SQL Server connection
python database/sql_connection.py

# Run complete pipeline with SQL Server integration
python scripts/run_pipeline.py run --use-database

# Run specific steps
python scripts/run_pipeline.py step --step ingestion
python scripts/run_pipeline.py step --step modeling
python scripts/run_pipeline.py step --step sql_export

# Start automated hourly schedule
python scripts/run_pipeline.py schedule --use-database

# Check pipeline status
python scripts/run_pipeline.py status
```

### **4. Monitor Pipeline Health**
```bash
# Single health check
python monitor_pipeline.py check

# Continuous monitoring
python monitor_pipeline.py monitor --interval 300
```

### **5. Power BI DirectQuery Dashboard**
```bash
# Connect Power BI to SQL Server using DirectQuery
# Follow the complete guide: powerbi/DIRECTQUERY_SETUP.md

# Key SQL Server views for Power BI:
vw_topic_summary              # Topic overview and KPIs
vw_daily_trends_enhanced      # Daily topic trends with rolling averages
vw_hourly_trends_enhanced     # Hourly patterns and time analysis
vw_recent_articles            # Latest classified articles
vw_source_performance         # News source analysis
vw_confidence_distribution    # Model performance metrics
vw_pipeline_health            # Real-time pipeline monitoring
```

### **DirectQuery Benefits**
- ✅ **Real-time data** - No manual refresh needed
- ✅ **Live dashboards** - Data updates automatically
- ✅ **Scalable performance** - Optimized SQL views
- ✅ **Enterprise ready** - SQL Server backend

## 🤖 **Automation & Scheduling**

### **Windows Task Scheduler**
```bash
# Import the pre-configured task
schtasks /create /xml scheduler/windows_task.xml /tn "TopicModelingPipeline"

# Or manually configure:
# - Program: python
# - Arguments: scripts/run_pipeline.py run --use-database
# - Trigger: Hourly at :05 minutes
```

### **Linux/Mac Cron Jobs**
```bash
# Edit crontab
crontab -e

# Add hourly execution (5 minutes past each hour)
5 * * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py run --use-database >> logs/cron.log 2>&1
```

### **Python Scheduler (Cross-platform)**
```bash
# Built-in Python scheduler with SQL Server
python scripts/run_pipeline.py schedule --use-database --frequency hourly --time :05
```

## 🗄️ **SQL Server Architecture**

### **Database Schema**
```sql
-- Core Tables
articles                    # Master articles table with metadata
topics                     # Topics master table with labels and keywords
article_topics             # Article-topic classifications with confidence

-- Aggregated Tables (for performance)
topic_trends_daily         # Pre-calculated daily topic trends
topic_trends_hourly        # Pre-calculated hourly topic patterns
source_analysis            # Source performance and diversity metrics
confidence_analysis        # Model confidence distribution analysis

-- Monitoring Tables
pipeline_runs              # Pipeline execution tracking
data_quality_metrics       # Data quality and health metrics
```

### **Optimized Views for Power BI**
```sql
-- Real-time Dashboard Views
vw_topic_summary           # Topic overview with KPIs
vw_kpi_metrics            # Key performance indicators
vw_daily_trends_enhanced   # Daily trends with rolling averages
vw_hourly_trends_enhanced  # Hourly patterns with time intelligence
vw_recent_articles         # Latest articles with enriched metadata
vw_source_performance      # Source quality and diversity analysis
vw_confidence_distribution # Model performance metrics
vw_model_performance       # Real-time model health indicators
vw_pipeline_health         # Pipeline monitoring and status
vw_data_freshness         # Data freshness indicators
```

### **Performance Optimizations**
- ✅ **Clustered indexes** on time-based columns
- ✅ **Covering indexes** for common query patterns
- ✅ **Pre-aggregated tables** for fast dashboard loading
- ✅ **Stored procedures** for efficient upsert operations
- ✅ **Connection pooling** for high-performance data access
- ✅ **Bulk insert operations** for large data volumes

## 📊 **Monitoring & Health Checks**

### **Pipeline Health Monitoring**
```bash
# Single health check
python monitor_pipeline.py check

# Continuous monitoring (every 5 minutes)
python monitor_pipeline.py monitor --interval 300

# View monitoring logs
tail -f logs/monitor.log
```

### **Health Check Features**
- ✅ Recent execution status
- ✅ Success rate tracking
- ✅ Data freshness validation
- ✅ Model performance metrics
- ✅ Storage health monitoring
- ✅ Component availability checks
- ✅ Email alerts for critical issues

### **Status Reporting**
```bash
# Detailed pipeline status
python scripts/run_pipeline.py status

# View status report
cat logs/pipeline_status.json
```

## 🛠️ **Troubleshooting**

### **Common Issues**

**Model Not Found:**
```bash
❌ Error: No LDA model found
💡 Solution: Place your .pkl file in models/lda_model.pkl
💡 Validate: python setup.py
```

**API Key Issues:**
```bash
❌ Error: NEWS_API_KEY not found
💡 Solution: Set API key in config/.env
💡 Test: python scripts/run_pipeline.py step --step ingestion
```

**No Articles Collected:**
```bash
❌ Error: No articles fetched
💡 Check: API key validity and rate limits
💡 Check: Network connectivity
💡 Check: Query keywords in config/pipeline.yaml
```

**Classification Failures:**
```bash
❌ Error: Classification failed
💡 Check: Model compatibility with current scikit-learn version
💡 Check: Text preprocessing pipeline
💡 Check: Model file integrity
```

**Power BI Data Issues:**
```bash
❌ Error: Empty Power BI exports
💡 Check: Processed articles exist in data/processed_articles.csv
💡 Run: python scripts/run_pipeline.py step --step export
💡 Check: logs/pipeline.log for detailed errors
```

### **Log Files**

| Log File | Purpose |
|----------|---------|
| `logs/pipeline.log` | Main pipeline execution logs |
| `logs/monitor.log` | Health monitoring logs |
| `logs/alerts.log` | Critical alerts and warnings |
| `logs/pipeline_status.json` | Current pipeline status |
| `logs/cron.log` | Scheduled execution logs |

### **Data Validation**

```bash
# Check data integrity
python -c "
import pandas as pd
raw = pd.read_csv('data/raw_articles.csv')
processed = pd.read_csv('data/processed_articles.csv')
print(f'Raw articles: {len(raw)}')
print(f'Processed articles: {len(processed)}')
print(f'Processing backlog: {len(raw) - len(processed)}')
"

# Verify Power BI exports
ls -la data/powerbi/*.csv
```

## 🔮 **Future Enhancements**

### **Planned Features**
- 🔄 **Real-time streaming** with Apache Kafka
- 🧠 **Sentiment analysis** integration
- 🌍 **Multi-language support**
- 📱 **Mobile dashboard** optimization
- 🔗 **API endpoints** for external integration
- 📈 **Advanced analytics** with trend prediction

### **Scalability Options**
- 🗄️ **PostgreSQL/MySQL** for large-scale storage
- ☁️ **Cloud deployment** (AWS/Azure/GCP)
- 🐳 **Docker containerization**
- 🔄 **Kubernetes orchestration**
- 📊 **Elasticsearch + Kibana** for advanced analytics

---

## 📞 **Support & Documentation**

### **Quick Help**
- 📋 **Setup Issues**: Run `python setup.py` for validation
- 🔧 **Pipeline Issues**: Check `logs/pipeline.log`
- 📊 **Power BI Issues**: See `powerbi/POWERBI_SETUP.md`
- 🏥 **Health Issues**: Run `python monitor_pipeline.py check`

### **Documentation**
- 📖 **Power BI Setup**: `powerbi/POWERBI_SETUP.md`
- ⏰ **Scheduling**: `scheduler/` directory
- 🔧 **Configuration**: `config/pipeline.yaml`

---

**🎉 Your comprehensive real-time topic modeling system is ready!**

**Built specifically for your trained LDA model with production-ready automation, monitoring, and Power BI integration.**

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Memory**: 4 GB RAM
- **Storage**: 5 GB free space
- **OS**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Recommended for Production
- **Python**: 3.9 or 3.10
- **Memory**: 8 GB RAM or more
- **Storage**: 20 GB free space (SSD preferred)
- **CPU**: Multi-core processor

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │   Processing    │    │     Output      │
│                 │    │                 │    │                 │
│ • NewsAPI       │───▶│ • Text Cleaning │───▶│ • CSV Files     │
│ • RSS Feeds     │    │ • Tokenization  │    │ • Power BI Data │
│ • Custom APIs   │    │ • Topic Modeling│    │ • Visualizations│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduling    │    │    Monitoring   │    │   Integration   │
│                 │    │                 │    │                 │
│ • Cron Jobs     │    │ • Logging       │    │ • Power BI      │
│ • Task Scheduler│    │ • Health Checks │    │ • Dashboards    │
│ • Cloud Triggers│    │ • Alerting      │    │ • APIs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Sample Output

The pipeline generates structured data perfect for analysis:

### Topic Distribution Example
| Topic ID | Topic Label | Keywords | Article Count | Confidence |
|----------|-------------|----------|---------------|------------|
| 0 | Technology_AI_Innovation | artificial, intelligence, technology | 45 | 0.87 |
| 1 | Politics_Election_Democracy | election, voting, political | 38 | 0.82 |
| 2 | Economy_Market_Financial | market, economic, financial | 42 | 0.79 |

### Time Series Trends
| Date | Topic | Article Count | Avg Confidence |
|------|-------|---------------|----------------|
| 2024-01-01 | Technology_AI | 12 | 0.85 |
| 2024-01-01 | Politics_Election | 8 | 0.78 |
| 2024-01-02 | Technology_AI | 15 | 0.88 |
