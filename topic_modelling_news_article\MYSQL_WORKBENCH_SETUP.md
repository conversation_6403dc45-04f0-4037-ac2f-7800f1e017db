# 🛠️ MySQL Workbench Setup Guide for Topic Modeling Pipeline

## 🎯 **Overview**
This guide helps you set up MySQL database using MySQL Workbench for the topic modeling pipeline.

## 📋 **Prerequisites**
- ✅ MySQL Workbench installed on your system
- ✅ MySQL Server running (local or remote)
- ✅ Administrative access to MySQL

## 🚀 **Step-by-Step Setup**

### **Step 1: Create Database Connection**

1. **Open MySQL Workbench**
2. **Click the "+" icon** next to "MySQL Connections"
3. **Configure Connection:**
   ```
   Connection Name: TopicModeling
   Connection Method: Standard (TCP/IP)
   Hostname: localhost
   Port: 3306
   Username: root (or your MySQL username)
   ```
4. **Click "Store in Vault"** to save password
5. **Test Connection** - should show "Successfully made the MySQL connection"
6. **Click "OK"** to save

### **Step 2: Create Database and User**

1. **Double-click** your new connection to open it
2. **Run these commands** in the SQL editor:

```sql
-- Create the database
CREATE DATABASE IF NOT EXISTS TopicModelingDB;

-- Create a dedicated user for the application
CREATE USER 'topic_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON TopicModelingDB.* TO 'topic_user'@'localhost';

-- Apply the changes
FLUSH PRIVILEGES;

-- Verify the database was created
SHOW DATABASES;
```

3. **Execute** each command by selecting it and pressing **Ctrl+Enter**

### **Step 3: Deploy Database Schema**

1. **Select TopicModelingDB:**
   ```sql
   USE TopicModelingDB;
   ```

2. **Open the schema file:**
   - Go to **File → Open SQL Script**
   - Navigate to your project folder
   - Open `database/mysql_complete_setup.sql`

3. **Execute the entire script:**
   - Press **Ctrl+Shift+Enter** to run all commands
   - Wait for completion (should take 10-30 seconds)

4. **Verify tables were created:**
   ```sql
   SHOW TABLES;
   ```
   
   You should see:
   ```
   raw_articles
   preprocessed_articles
   topics
   topic_results
   vw_topic_summary
   vw_kpi_metrics
   vw_daily_trends_enhanced
   vw_hourly_trends_enhanced
   vw_recent_articles
   vw_source_performance
   ```

### **Step 4: Configure Application Connection**

1. **Update database configuration:**
   - Open `config/database.json` in your project
   - Update with your MySQL credentials:

```json
{
  "server": "localhost",
  "database": "TopicModelingDB",
  "username": "topic_user",
  "password": "your_secure_password",
  "port": 3306,
  "driver": "mysql+pymysql",
  "pool_size": 10,
  "max_overflow": 20,
  "pool_timeout": 30
}
```

### **Step 5: Test the Connection**

1. **Open Command Prompt** in your project directory
2. **Run the connection test:**
   ```bash
   cd database
   python test_connection.py
   ```

3. **Expected output:**
   ```
   ✅ Configuration file found
   ✅ Connection manager created successfully
   ✅ Basic connection test passed
   ✅ Connected to database: TopicModelingDB
   ✅ Table 'raw_articles' exists
   ✅ Table 'preprocessed_articles' exists
   ✅ Table 'topics' exists
   ✅ Table 'topic_results' exists
   ✅ All Power BI views exist and queryable
   ```

## 🔍 **Verification Queries**

Run these queries in MySQL Workbench to verify your setup:

### **Check Database Structure:**
```sql
USE TopicModelingDB;

-- Show all tables and views
SHOW FULL TABLES;

-- Check table structures
DESCRIBE raw_articles;
DESCRIBE preprocessed_articles;
DESCRIBE topic_results;
DESCRIBE topics;
```

### **Check Sample Data:**
```sql
-- Check if sample topics were inserted
SELECT * FROM topics;

-- Check indexes
SHOW INDEX FROM raw_articles;
SHOW INDEX FROM preprocessed_articles;
SHOW INDEX FROM topic_results;
```

### **Test Views:**
```sql
-- Test Power BI views
SELECT * FROM vw_topic_summary LIMIT 5;
SELECT * FROM vw_kpi_metrics LIMIT 5;
```

## 🚨 **Troubleshooting**

### **Common Issues:**

**1. "Access denied for user"**
```sql
-- Reset user password
ALTER USER 'topic_user'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

**2. "Database doesn't exist"**
```sql
-- Recreate database
DROP DATABASE IF EXISTS TopicModelingDB;
CREATE DATABASE TopicModelingDB;
```

**3. "Table doesn't exist"**
- Re-run the schema file: `database/mysql_complete_setup.sql`
- Check for SQL errors in the output

**4. "Connection refused"**
- Verify MySQL service is running
- Check port 3306 is not blocked
- Try connecting with different credentials

### **Performance Optimization:**

**1. Check MySQL Configuration:**
```sql
-- Check current settings
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'max_connections';
```

**2. Optimize for Topic Modeling:**
```sql
-- Add to my.cnf or my.ini
[mysqld]
innodb_buffer_pool_size = 1G
max_connections = 200
query_cache_size = 64M
```

## 📊 **Power BI Integration**

### **DirectQuery Connection String:**
```
Server: localhost
Database: TopicModelingDB
Data Connectivity mode: DirectQuery
Authentication: Database
Username: topic_user
Password: [your_password]
```

### **Recommended Views for Power BI:**
- `vw_topic_summary` - Main dashboard overview
- `vw_kpi_metrics` - Key performance indicators  
- `vw_daily_trends_enhanced` - Daily trend analysis
- `vw_hourly_trends_enhanced` - Hourly patterns
- `vw_recent_articles` - Latest articles feed

## 🎯 **Next Steps**

After successful MySQL setup:

1. **Test the pipeline:**
   ```bash
   python automated_pipeline.py --single-run
   ```

2. **Start continuous processing:**
   ```bash
   python automated_pipeline.py --continuous
   ```

3. **Monitor pipeline health:**
   ```bash
   python monitor_pipeline.py check
   ```

4. **Connect Power BI:**
   - Follow `powerbi/DIRECTQUERY_SETUP.md`
   - Use the views created in this setup

## 📞 **Support**

If you encounter issues:
1. Check MySQL error logs
2. Verify network connectivity
3. Test with MySQL command line client
4. Review application logs in `logs/` directory

Your MySQL Workbench setup is now complete and ready for the topic modeling pipeline!
