# 🏗️ MySQL Schema Design Decision Guide

## 🎯 **Schema Design Principles for Topic Modeling**

### **1. Data Volume Considerations**

**Expected Data Volumes:**
```
Raw Articles:     ~2,400 articles/day (100/hour × 24 hours)
Preprocessed:     ~2,400 records/day (1:1 with raw)
Topic Results:    ~2,400 records/day (1:1 with preprocessed)
Annual Growth:    ~876,000 articles/year
```

**Storage Requirements:**
```
Raw Articles:     ~500KB per article (with full content)
Preprocessed:     ~50KB per article (cleaned text)
Topic Results:    ~5KB per article (topic data)
Total per year:   ~500GB estimated
```

### **2. Primary Key Strategy**

**Decision Matrix:**

| Table | Key Type | Rationale |
|-------|----------|-----------|
| `raw_articles` | VARCHAR(50) | MD5 hash for deduplication |
| `preprocessed_articles` | BIGINT AUTO_INCREMENT | High volume, need speed |
| `topic_results` | BIGINT AUTO_INCREMENT | Highest volume table |
| `topics` | INT | Small lookup table |

**Why These Choices:**
```sql
-- Raw articles: Content-based key for deduplication
article_id VARCHAR(50) PRIMARY KEY  -- MD5 of URL + timestamp

-- Preprocessed: Auto-increment for performance
preprocessed_id BIGINT AUTO_INCREMENT PRIMARY KEY  -- Handles billions of records

-- Topic results: Auto-increment for massive scale
result_id BIGINT AUTO_INCREMENT PRIMARY KEY  -- Fastest inserts
```

### **3. Data Type Decisions**

**Text Field Sizing:**
```sql
-- Based on real-world news analysis
title VARCHAR(500)          -- 95% of news titles < 500 chars
description TEXT            -- Up to 65,535 chars (sufficient for summaries)
content LONGTEXT           -- Up to 4GB (handles full articles + metadata)
url VARCHAR(1000)          -- Handles complex URLs with parameters
```

**Numeric Precision:**
```sql
-- LDA confidence scores need high precision
confidence DECIMAL(8,6)    -- Range: 0.000001 to 99.999999
                          -- Captures subtle confidence differences

-- Performance metrics
word_count INT             -- Sufficient for article word counts
preprocessing_duration_ms INT  -- Millisecond precision for optimization
```

**Timestamp Strategy:**
```sql
-- All timestamps use DATETIME for consistency
published_at DATETIME      -- Article publication (from NewsAPI)
fetched_at DATETIME        -- Pipeline ingestion time
preprocessing_timestamp DATETIME  -- Processing completion
classification_timestamp DATETIME  -- Topic modeling completion
```

### **4. Indexing Strategy**

**Performance-Critical Indexes:**

```sql
-- Time-based queries (most common in dashboards)
INDEX IX_published_timestamp (published_at DESC, processing_status)
-- Supports: "Show recent articles by status"

INDEX IX_classification_timestamp (classification_timestamp DESC, topic_id, confidence DESC)  
-- Supports: "Show recent classifications by topic and confidence"

-- Deduplication (critical for data integrity)
INDEX IX_content_hash (content_hash)
-- Supports: Fast duplicate detection

-- Foreign key performance
INDEX IX_preprocessing_timestamp (preprocessing_timestamp DESC)
-- Supports: Joining preprocessed with raw articles
```

**Index Size Estimation:**
```
IX_published_timestamp:     ~50MB per million articles
IX_content_hash:           ~30MB per million articles  
IX_classification_timestamp: ~80MB per million articles
Total index overhead:      ~15% of table size
```

### **5. Relationship Design**

**Foreign Key Strategy:**
```sql
-- Cascade deletes for data consistency
FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE

-- Why CASCADE: If raw article is deleted, all derived data should be removed
-- Alternative: RESTRICT (prevent deletion if dependencies exist)
```

**Unique Constraints:**
```sql
-- Prevent duplicate processing
UNIQUE KEY UQ_article_preprocessing (article_id)
UNIQUE KEY UQ_preprocessed_classification (preprocessed_id)

-- Prevent duplicate content
UNIQUE (url)
UNIQUE (content_hash)
```

### **6. Normalization vs. Denormalization**

**Normalized Design (Current):**
```
✅ Pros:
- No data duplication
- Consistent updates
- Smaller storage footprint
- Clear data lineage

❌ Cons:  
- Requires JOINs for complex queries
- Slightly slower for dashboard queries
```

**Denormalized Alternative:**
```sql
-- Could combine into single table for Power BI performance
CREATE TABLE articles_complete (
    article_id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(500),
    content LONGTEXT,
    cleaned_text LONGTEXT,
    topic_id INT,
    confidence DECIMAL(8,6),
    published_at DATETIME,
    classification_timestamp DATETIME
);
```

**Decision: Stay Normalized**
- Better for data integrity
- Power BI views handle JOIN performance
- Easier to maintain and debug

### **7. JSON vs. Relational Storage**

**Raw JSON Storage:**
```sql
raw_json LONGTEXT  -- Store complete NewsAPI response
```

**Benefits:**
- Preserves all original data
- Future-proof for new API fields
- Debugging and audit trail
- Can extract new fields later

**Topic Distribution Storage:**
```sql
topic_distribution LONGTEXT  -- JSON array of topic probabilities
-- Example: [{"topic_1": 0.45}, {"topic_2": 0.35}, {"topic_3": 0.20}]
```

### **8. Performance Optimization Decisions**

**Connection Pooling:**
```python
pool_size = 10          # Base connections for normal load
max_overflow = 20       # Burst capacity for peak processing
pool_timeout = 30       # Reasonable wait time
pool_pre_ping = True    # Validate connections (prevents errors)
```

**Bulk Operations:**
```python
# Insert 1000 articles at once instead of individual INSERTs
# 100x faster than row-by-row processing
sql_manager.bulk_insert('raw_articles', articles_batch)
```

**Query Optimization:**
```sql
-- Use covering indexes for dashboard queries
INDEX IX_dashboard_summary (topic_id, classification_timestamp DESC, confidence)
-- Covers: SELECT topic_id, COUNT(*), AVG(confidence) GROUP BY topic_id
```

### **9. Scalability Considerations**

**Partitioning Strategy (Future):**
```sql
-- Partition by month for time-series data
PARTITION BY RANGE (YEAR(published_at) * 100 + MONTH(published_at))
(
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    ...
);
```

**Archive Strategy:**
```sql
-- Move old data to archive tables after 2 years
CREATE TABLE raw_articles_archive LIKE raw_articles;
-- Automated monthly archival process
```

### **10. Data Quality Constraints**

**Business Rules Enforcement:**
```sql
-- Ensure confidence scores are valid
CHECK (confidence >= 0.0 AND confidence <= 1.0)

-- Ensure processing order
CHECK (preprocessing_timestamp >= fetched_at)
CHECK (classification_timestamp >= preprocessing_timestamp)

-- Ensure content exists
CHECK (LENGTH(TRIM(title)) > 0)
CHECK (LENGTH(TRIM(content)) > 10)
```

### **11. Monitoring and Maintenance**

**Health Check Queries:**
```sql
-- Data freshness
SELECT MAX(fetched_at) as last_fetch FROM raw_articles;

-- Processing backlog  
SELECT COUNT(*) as pending FROM raw_articles WHERE processing_status = 'pending';

-- Error rate
SELECT 
    COUNT(CASE WHEN processing_status = 'failed' THEN 1 END) / COUNT(*) as error_rate
FROM raw_articles 
WHERE fetched_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);
```

**Maintenance Tasks:**
```sql
-- Weekly index optimization
OPTIMIZE TABLE raw_articles, preprocessed_articles, topic_results;

-- Monthly statistics update
ANALYZE TABLE raw_articles, preprocessed_articles, topic_results;
```

## 🎯 **Final Schema Recommendations**

**For Your Use Case:**
1. ✅ **Keep current normalized design** - Best for data integrity
2. ✅ **Use BIGINT for high-volume tables** - Future-proof
3. ✅ **Store raw JSON** - Preserves all data
4. ✅ **Time-based indexing** - Optimized for dashboard queries
5. ✅ **CASCADE foreign keys** - Maintains consistency
6. ✅ **Connection pooling** - Handles concurrent access
7. ✅ **Bulk operations** - Maximizes throughput

This schema design balances performance, scalability, and maintainability for your real-time topic modeling pipeline.
