#!/usr/bin/env python3
"""
Quick MySQL Setup and Configuration Tool
Helps configure MySQL connection for topic modeling pipeline
"""

import os
import sys
import json
import subprocess
from pathlib import Path
import getpass

def print_header(title):
    """Print formatted header."""
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def print_step(step_num, title):
    """Print formatted step."""
    print(f"\n📋 Step {step_num}: {title}")
    print("-" * 40)

def check_mysql_workbench():
    """Check if MySQL Workbench is available."""
    print_step(1, "Checking MySQL Workbench Installation")
    
    # Common MySQL Workbench paths
    workbench_paths = [
        r"C:\Program Files\MySQL\MySQL Workbench 8.0 CE\MySQLWorkbench.exe",
        r"C:\Program Files (x86)\MySQL\MySQL Workbench 8.0 CE\MySQLWorkbench.exe",
        r"C:\Program Files\MySQL\MySQL Workbench 8.0\MySQLWorkbench.exe"
    ]
    
    for path in workbench_paths:
        if os.path.exists(path):
            print(f"✅ MySQL Workbench found at: {path}")
            return path
    
    print("⚠️ MySQL Workbench not found in standard locations")
    print("💡 Please ensure MySQL Workbench is installed")
    return None

def check_mysql_service():
    """Check if MySQL service is running."""
    print_step(2, "Checking MySQL Service")
    
    try:
        # Try to check MySQL service status on Windows
        result = subprocess.run(['sc', 'query', 'mysql'], 
                              capture_output=True, text=True)
        
        if 'RUNNING' in result.stdout:
            print("✅ MySQL service is running")
            return True
        else:
            print("⚠️ MySQL service is not running")
            print("💡 Start MySQL service or XAMPP")
            return False
            
    except Exception as e:
        print(f"⚠️ Could not check MySQL service: {e}")
        print("💡 Please ensure MySQL server is running")
        return False

def create_database_config():
    """Create database configuration file."""
    print_step(3, "Creating Database Configuration")
    
    config_dir = Path("config")
    config_file = config_dir / "database.json"
    
    if config_file.exists():
        print("✅ Database configuration already exists")
        with open(config_file, 'r') as f:
            config = json.load(f)
        print(f"   Server: {config.get('server', 'Not set')}")
        print(f"   Database: {config.get('database', 'Not set')}")
        print(f"   Username: {config.get('username', 'Not set')}")
        
        update = input("\n🔄 Update configuration? (y/n): ").lower().strip()
        if update != 'y':
            return config
    
    print("\n📝 Please provide MySQL connection details:")
    
    # Get connection details
    server = input("MySQL Server (localhost): ").strip() or "localhost"
    port = input("MySQL Port (3306): ").strip() or "3306"
    database = input("Database Name (TopicModelingDB): ").strip() or "TopicModelingDB"
    username = input("MySQL Username: ").strip()
    
    if not username:
        print("❌ Username is required")
        return None
    
    password = getpass.getpass("MySQL Password: ")
    
    # Create configuration
    config = {
        "server": server,
        "database": database,
        "username": username,
        "password": password,
        "port": int(port),
        "driver": "mysql+pymysql",
        "pool_size": 10,
        "max_overflow": 20,
        "pool_timeout": 30
    }
    
    # Save configuration
    config_dir.mkdir(exist_ok=True)
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration saved to {config_file}")
    return config

def test_connection(config):
    """Test database connection."""
    print_step(4, "Testing Database Connection")
    
    try:
        # Import and test connection
        sys.path.append('database')
        from sql_connection import SQLConnectionManager
        
        # Create connection manager
        sql_manager = SQLConnectionManager(**config)
        
        # Test basic connection
        with sql_manager.get_connection() as conn:
            result = conn.execute("SELECT 1 as test").fetchone()
            if result:
                print("✅ Database connection successful")
                return True
            
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\n💡 Troubleshooting:")
        print("   1. Check MySQL server is running")
        print("   2. Verify username and password")
        print("   3. Ensure database exists")
        return False

def check_database_schema(config):
    """Check if database schema exists."""
    print_step(5, "Checking Database Schema")
    
    try:
        sys.path.append('database')
        from sql_connection import SQLConnectionManager
        
        sql_manager = SQLConnectionManager(**config)
        
        # Check if tables exist
        with sql_manager.get_connection() as conn:
            tables = conn.execute("SHOW TABLES").fetchall()
            table_names = [table[0] for table in tables]
            
            required_tables = ['raw_articles', 'preprocessed_articles', 'topics', 'topic_results']
            missing_tables = [table for table in required_tables if table not in table_names]
            
            if not missing_tables:
                print("✅ All required tables exist")
                return True
            else:
                print(f"⚠️ Missing tables: {missing_tables}")
                print("💡 Run database schema setup")
                return False
                
    except Exception as e:
        print(f"❌ Schema check failed: {e}")
        return False

def setup_database_schema(config):
    """Setup database schema."""
    print_step(6, "Setting Up Database Schema")
    
    schema_file = Path("database/mysql_complete_setup.sql")
    if not schema_file.exists():
        print(f"❌ Schema file not found: {schema_file}")
        return False
    
    try:
        # Run schema setup
        cmd = [
            'mysql',
            f'-h{config["server"]}',
            f'-P{config["port"]}',
            f'-u{config["username"]}',
            f'-p{config["password"]}',
            config["database"]
        ]
        
        with open(schema_file, 'r') as f:
            result = subprocess.run(cmd, input=f.read(), text=True, 
                                  capture_output=True)
        
        if result.returncode == 0:
            print("✅ Database schema created successfully")
            return True
        else:
            print(f"❌ Schema setup failed: {result.stderr}")
            print("💡 Try running the schema manually in MySQL Workbench")
            return False
            
    except FileNotFoundError:
        print("❌ MySQL command line client not found")
        print("💡 Please run the schema manually in MySQL Workbench:")
        print(f"   1. Open {schema_file}")
        print("   2. Execute all commands")
        return False
    except Exception as e:
        print(f"❌ Schema setup error: {e}")
        return False

def create_env_file():
    """Create environment file."""
    print_step(7, "Creating Environment Configuration")
    
    env_file = Path("config/.env")
    
    if env_file.exists():
        print("✅ Environment file already exists")
        return
    
    # Create .env file
    env_content = """# News API Configuration
NEWS_API_KEY=your_newsapi_key_here

# MySQL Configuration (optional - can use database.json instead)
MYSQL_SERVER=localhost
MYSQL_DATABASE=TopicModelingDB
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_PORT=3306
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Environment file created: {env_file}")
    print("💡 Please edit the file with your actual API key")

def main():
    """Main setup function."""
    print_header("MySQL Setup for Topic Modeling Pipeline")
    
    # Change to script directory
    os.chdir(Path(__file__).parent)
    
    # Step 1: Check MySQL Workbench
    workbench_path = check_mysql_workbench()
    
    # Step 2: Check MySQL Service
    mysql_running = check_mysql_service()
    
    if not mysql_running:
        print("\n❗ Please start MySQL service before continuing")
        input("Press Enter when MySQL is running...")
    
    # Step 3: Create database config
    config = create_database_config()
    if not config:
        print("❌ Configuration setup failed")
        return 1
    
    # Step 4: Test connection
    if not test_connection(config):
        print("❌ Connection test failed")
        return 1
    
    # Step 5: Check schema
    schema_exists = check_database_schema(config)
    
    # Step 6: Setup schema if needed
    if not schema_exists:
        setup_choice = input("\n🔧 Setup database schema now? (y/n): ").lower().strip()
        if setup_choice == 'y':
            setup_database_schema(config)
    
    # Step 7: Create env file
    create_env_file()
    
    # Final summary
    print_header("Setup Complete!")
    print("✅ MySQL connection configured")
    print("✅ Database configuration saved")
    print("✅ Environment file created")
    
    print("\n🚀 Next Steps:")
    print("1. Edit config/.env with your NewsAPI key")
    print("2. Place your LDA model in models/lda_model.pkl")
    print("3. Run: python automated_pipeline.py --single-run")
    
    print("\n📖 For detailed MySQL Workbench setup:")
    print("   See: MYSQL_WORKBENCH_SETUP.md")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
