#!/usr/bin/env python3
"""
Comprehensive Pipeline Monitoring & Logging System
Real-time monitoring, alerting, and health checks for the topic modeling pipeline
"""

import os
import sys
import json
import time
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import pandas as pd
import sqlite3

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PipelineMonitor:
    """Comprehensive pipeline monitoring and alerting system."""
    
    def __init__(self, 
                 data_dir: str = "data",
                 logs_dir: str = "logs",
                 alert_config: Dict = None):
        """
        Initialize pipeline monitor.
        
        Args:
            data_dir: Directory containing pipeline data
            logs_dir: Directory containing log files
            alert_config: Configuration for alerts (email, etc.)
        """
        self.data_dir = Path(data_dir)
        self.logs_dir = Path(logs_dir)
        self.alert_config = alert_config or {}
        
        # Create directories
        self.logs_dir.mkdir(exist_ok=True)
        
        # File paths
        self.status_file = self.logs_dir / "pipeline_status.json"
        self.monitor_log = self.logs_dir / "monitor.log"
        self.alerts_log = self.logs_dir / "alerts.log"
        
        # Monitoring thresholds
        self.thresholds = {
            'max_hours_since_success': 3,  # Alert if no success in 3 hours
            'min_articles_per_hour': 5,    # Alert if less than 5 articles/hour
            'max_error_rate': 0.5,         # Alert if error rate > 50%
            'max_processing_time': 1800,   # Alert if processing takes > 30 minutes
            'min_confidence': 0.3,         # Alert if avg confidence < 0.3
            'max_disk_usage_gb': 10        # Alert if data directory > 10GB
        }
        
        # Setup monitor logging
        self._setup_monitor_logging()
        
        logger.info("📊 Pipeline monitor initialized")
    
    def _setup_monitor_logging(self):
        """Setup dedicated monitor logging."""
        
        # Create monitor logger
        self.monitor_logger = logging.getLogger('monitor')
        self.monitor_logger.setLevel(logging.INFO)
        
        # File handler for monitor logs
        monitor_handler = logging.FileHandler(self.monitor_log)
        monitor_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        self.monitor_logger.addHandler(monitor_handler)
        
        # Create alerts logger
        self.alerts_logger = logging.getLogger('alerts')
        self.alerts_logger.setLevel(logging.WARNING)
        
        # File handler for alerts
        alerts_handler = logging.FileHandler(self.alerts_log)
        alerts_handler.setFormatter(
            logging.Formatter('%(asctime)s - ALERT - %(message)s')
        )
        self.alerts_logger.addHandler(alerts_handler)
    
    def load_pipeline_status(self) -> Dict:
        """Load latest pipeline status."""
        
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r') as f:
                    return json.load(f)
            else:
                self.monitor_logger.warning("No pipeline status file found")
                return {}
        except Exception as e:
            self.monitor_logger.error(f"Error loading pipeline status: {e}")
            return {}
    
    def check_pipeline_health(self) -> Dict:
        """Perform comprehensive pipeline health check."""
        
        self.monitor_logger.info("🔍 Starting pipeline health check")
        
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_health': 'UNKNOWN',
            'checks': {},
            'alerts': [],
            'recommendations': []
        }
        
        # Load pipeline status
        status = self.load_pipeline_status()
        
        if not status:
            health_status['overall_health'] = 'CRITICAL'
            health_status['alerts'].append('No pipeline status available')
            return health_status
        
        # Check 1: Recent execution
        health_status['checks']['recent_execution'] = self._check_recent_execution(status)
        
        # Check 2: Success rate
        health_status['checks']['success_rate'] = self._check_success_rate(status)
        
        # Check 3: Data freshness
        health_status['checks']['data_freshness'] = self._check_data_freshness()
        
        # Check 4: Processing performance
        health_status['checks']['processing_performance'] = self._check_processing_performance()
        
        # Check 5: Model performance
        health_status['checks']['model_performance'] = self._check_model_performance()
        
        # Check 6: Storage health
        health_status['checks']['storage_health'] = self._check_storage_health()
        
        # Check 7: Component availability
        health_status['checks']['component_availability'] = self._check_component_availability()
        
        # Determine overall health
        health_status['overall_health'] = self._determine_overall_health(health_status['checks'])
        
        # Generate alerts and recommendations
        self._generate_alerts_and_recommendations(health_status)
        
        self.monitor_logger.info(f"✅ Health check complete: {health_status['overall_health']}")
        return health_status
    
    def _check_recent_execution(self, status: Dict) -> Dict:
        """Check if pipeline has run recently."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            stats = status.get('statistics', {})
            last_run = stats.get('last_run')
            last_success = stats.get('last_success')
            
            if not last_run:
                check_result['status'] = 'CRITICAL'
                check_result['message'] = 'Pipeline has never run'
                return check_result
            
            # Parse timestamps
            last_run_time = datetime.fromisoformat(last_run.replace('Z', '+00:00'))
            hours_since_run = (datetime.now() - last_run_time).total_seconds() / 3600
            
            check_result['details']['hours_since_last_run'] = round(hours_since_run, 2)
            
            if last_success:
                last_success_time = datetime.fromisoformat(last_success.replace('Z', '+00:00'))
                hours_since_success = (datetime.now() - last_success_time).total_seconds() / 3600
                check_result['details']['hours_since_last_success'] = round(hours_since_success, 2)
                
                if hours_since_success > self.thresholds['max_hours_since_success']:
                    check_result['status'] = 'CRITICAL'
                    check_result['message'] = f'No successful run in {hours_since_success:.1f} hours'
                else:
                    check_result['status'] = 'HEALTHY'
                    check_result['message'] = f'Last successful run {hours_since_success:.1f} hours ago'
            else:
                check_result['status'] = 'CRITICAL'
                check_result['message'] = 'Pipeline has never succeeded'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking recent execution: {e}'
        
        return check_result
    
    def _check_success_rate(self, status: Dict) -> Dict:
        """Check pipeline success rate."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            stats = status.get('statistics', {})
            total_runs = stats.get('runs', 0)
            errors = stats.get('errors', [])
            
            if total_runs == 0:
                check_result['status'] = 'WARNING'
                check_result['message'] = 'No pipeline runs recorded'
                return check_result
            
            error_count = len(errors)
            success_rate = (total_runs - error_count) / total_runs
            
            check_result['details']['total_runs'] = total_runs
            check_result['details']['error_count'] = error_count
            check_result['details']['success_rate'] = round(success_rate, 3)
            
            if success_rate < (1 - self.thresholds['max_error_rate']):
                check_result['status'] = 'CRITICAL'
                check_result['message'] = f'High error rate: {(1-success_rate)*100:.1f}%'
            elif success_rate < 0.8:
                check_result['status'] = 'WARNING'
                check_result['message'] = f'Moderate error rate: {(1-success_rate)*100:.1f}%'
            else:
                check_result['status'] = 'HEALTHY'
                check_result['message'] = f'Good success rate: {success_rate*100:.1f}%'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking success rate: {e}'
        
        return check_result
    
    def _check_data_freshness(self) -> Dict:
        """Check if data is fresh and up-to-date."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            # Check raw articles
            raw_file = self.data_dir / "raw_articles.csv"
            processed_file = self.data_dir / "processed_articles.csv"
            
            if not raw_file.exists():
                check_result['status'] = 'CRITICAL'
                check_result['message'] = 'No raw articles data found'
                return check_result
            
            # Check file modification times
            raw_mtime = datetime.fromtimestamp(raw_file.stat().st_mtime)
            hours_since_raw = (datetime.now() - raw_mtime).total_seconds() / 3600
            
            check_result['details']['hours_since_raw_update'] = round(hours_since_raw, 2)
            
            if processed_file.exists():
                processed_mtime = datetime.fromtimestamp(processed_file.stat().st_mtime)
                hours_since_processed = (datetime.now() - processed_mtime).total_seconds() / 3600
                check_result['details']['hours_since_processed_update'] = round(hours_since_processed, 2)
            
            # Check article counts
            try:
                raw_df = pd.read_csv(raw_file)
                check_result['details']['total_raw_articles'] = len(raw_df)
                
                if processed_file.exists():
                    processed_df = pd.read_csv(processed_file)
                    check_result['details']['total_processed_articles'] = len(processed_df)
                    check_result['details']['processing_backlog'] = len(raw_df) - len(processed_df)
            except Exception as e:
                self.monitor_logger.warning(f"Error reading article counts: {e}")
            
            # Determine status
            if hours_since_raw > 2:
                check_result['status'] = 'WARNING'
                check_result['message'] = f'Raw data not updated in {hours_since_raw:.1f} hours'
            else:
                check_result['status'] = 'HEALTHY'
                check_result['message'] = f'Data updated {hours_since_raw:.1f} hours ago'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking data freshness: {e}'
        
        return check_result
    
    def _check_processing_performance(self) -> Dict:
        """Check processing performance metrics."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            # Check recent processing times from logs
            pipeline_log = self.logs_dir / "pipeline.log"
            
            if not pipeline_log.exists():
                check_result['status'] = 'WARNING'
                check_result['message'] = 'No pipeline logs found'
                return check_result
            
            # Parse recent log entries for processing times
            # This is a simplified check - in practice, you'd parse actual log entries
            check_result['status'] = 'HEALTHY'
            check_result['message'] = 'Processing performance within normal range'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking processing performance: {e}'
        
        return check_result
    
    def _check_model_performance(self) -> Dict:
        """Check LDA model performance metrics."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            processed_file = self.data_dir / "processed_articles.csv"
            
            if not processed_file.exists():
                check_result['status'] = 'WARNING'
                check_result['message'] = 'No processed articles to analyze'
                return check_result
            
            # Load recent processed articles
            df = pd.read_csv(processed_file)
            
            if df.empty:
                check_result['status'] = 'WARNING'
                check_result['message'] = 'No processed articles found'
                return check_result
            
            # Filter successful classifications
            successful_df = df[df['topic_id'] != -1]
            
            if successful_df.empty:
                check_result['status'] = 'CRITICAL'
                check_result['message'] = 'No successful topic classifications'
                return check_result
            
            # Calculate metrics
            avg_confidence = successful_df['topic_confidence'].mean()
            classification_rate = len(successful_df) / len(df)
            
            check_result['details']['avg_confidence'] = round(avg_confidence, 3)
            check_result['details']['classification_rate'] = round(classification_rate, 3)
            check_result['details']['total_articles'] = len(df)
            check_result['details']['successful_classifications'] = len(successful_df)
            
            # Determine status
            if avg_confidence < self.thresholds['min_confidence']:
                check_result['status'] = 'WARNING'
                check_result['message'] = f'Low average confidence: {avg_confidence:.3f}'
            elif classification_rate < 0.7:
                check_result['status'] = 'WARNING'
                check_result['message'] = f'Low classification rate: {classification_rate*100:.1f}%'
            else:
                check_result['status'] = 'HEALTHY'
                check_result['message'] = f'Model performing well (confidence: {avg_confidence:.3f})'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking model performance: {e}'
        
        return check_result
    
    def _check_storage_health(self) -> Dict:
        """Check storage health and disk usage."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            # Calculate directory size
            total_size = 0
            file_count = 0
            
            for file_path in self.data_dir.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            size_gb = total_size / (1024**3)
            
            check_result['details']['total_size_gb'] = round(size_gb, 2)
            check_result['details']['file_count'] = file_count
            
            # Check disk usage
            if size_gb > self.thresholds['max_disk_usage_gb']:
                check_result['status'] = 'WARNING'
                check_result['message'] = f'High disk usage: {size_gb:.2f} GB'
            else:
                check_result['status'] = 'HEALTHY'
                check_result['message'] = f'Storage healthy: {size_gb:.2f} GB'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking storage health: {e}'
        
        return check_result
    
    def _check_component_availability(self) -> Dict:
        """Check availability of pipeline components."""
        
        check_result = {'status': 'UNKNOWN', 'message': '', 'details': {}}
        
        try:
            components = {
                'news_api_key': bool(os.getenv('NEWS_API_KEY')),
                'lda_model': Path('models/lda_model.pkl').exists(),
                'data_directory': self.data_dir.exists(),
                'logs_directory': self.logs_dir.exists()
            }
            
            check_result['details'] = components
            
            missing_components = [name for name, available in components.items() if not available]
            
            if missing_components:
                check_result['status'] = 'CRITICAL'
                check_result['message'] = f'Missing components: {", ".join(missing_components)}'
            else:
                check_result['status'] = 'HEALTHY'
                check_result['message'] = 'All components available'
            
        except Exception as e:
            check_result['status'] = 'ERROR'
            check_result['message'] = f'Error checking component availability: {e}'
        
        return check_result
    
    def _determine_overall_health(self, checks: Dict) -> str:
        """Determine overall health status from individual checks."""
        
        statuses = [check['status'] for check in checks.values()]
        
        if 'CRITICAL' in statuses:
            return 'CRITICAL'
        elif 'ERROR' in statuses:
            return 'ERROR'
        elif 'WARNING' in statuses:
            return 'WARNING'
        elif all(status == 'HEALTHY' for status in statuses):
            return 'HEALTHY'
        else:
            return 'UNKNOWN'
    
    def _generate_alerts_and_recommendations(self, health_status: Dict):
        """Generate alerts and recommendations based on health status."""
        
        # Generate alerts for critical issues
        for check_name, check_result in health_status['checks'].items():
            if check_result['status'] in ['CRITICAL', 'ERROR']:
                alert_message = f"{check_name.upper()}: {check_result['message']}"
                health_status['alerts'].append(alert_message)
                self.alerts_logger.warning(alert_message)
        
        # Generate recommendations
        if health_status['overall_health'] != 'HEALTHY':
            health_status['recommendations'].append('Review pipeline logs for detailed error information')
            health_status['recommendations'].append('Check system resources and network connectivity')
            health_status['recommendations'].append('Verify API keys and model files are accessible')
    
    def send_alert_email(self, health_status: Dict):
        """Send alert email if configured."""
        
        if not self.alert_config.get('email_enabled', False):
            return
        
        if health_status['overall_health'] in ['CRITICAL', 'ERROR']:
            try:
                # Email configuration
                smtp_server = self.alert_config.get('smtp_server', 'smtp.gmail.com')
                smtp_port = self.alert_config.get('smtp_port', 587)
                email_user = self.alert_config.get('email_user')
                email_password = self.alert_config.get('email_password')
                alert_recipients = self.alert_config.get('alert_recipients', [])
                
                if not all([email_user, email_password, alert_recipients]):
                    self.monitor_logger.warning("Email alert configuration incomplete")
                    return
                
                # Create email
                msg = MimeMultipart()
                msg['From'] = email_user
                msg['To'] = ', '.join(alert_recipients)
                msg['Subject'] = f"Pipeline Alert: {health_status['overall_health']}"
                
                # Email body
                body = f"""
Pipeline Health Alert

Status: {health_status['overall_health']}
Timestamp: {health_status['timestamp']}

Alerts:
{chr(10).join(f"- {alert}" for alert in health_status['alerts'])}

Recommendations:
{chr(10).join(f"- {rec}" for rec in health_status['recommendations'])}

Please check the pipeline logs for more details.
                """
                
                msg.attach(MimeText(body, 'plain'))
                
                # Send email
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()
                server.login(email_user, email_password)
                server.send_message(msg)
                server.quit()
                
                self.monitor_logger.info("Alert email sent successfully")
                
            except Exception as e:
                self.monitor_logger.error(f"Failed to send alert email: {e}")
    
    def run_continuous_monitoring(self, check_interval: int = 300):
        """Run continuous monitoring with specified interval."""
        
        self.monitor_logger.info(f"🔄 Starting continuous monitoring (interval: {check_interval}s)")
        
        try:
            while True:
                health_status = self.check_pipeline_health()
                
                # Send alerts if needed
                self.send_alert_email(health_status)
                
                # Save health status
                health_file = self.logs_dir / "health_status.json"
                with open(health_file, 'w') as f:
                    json.dump(health_status, f, indent=2)
                
                # Wait for next check
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.monitor_logger.info("⏹️ Stopping continuous monitoring")

def main():
    """Main monitoring entry point."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="Pipeline Monitor")
    parser.add_argument('command', choices=['check', 'monitor'], 
                       help='Command to execute')
    parser.add_argument('--interval', type=int, default=300,
                       help='Monitoring interval in seconds')
    
    args = parser.parse_args()
    
    # Initialize monitor
    monitor = PipelineMonitor()
    
    if args.command == 'check':
        # Single health check
        health_status = monitor.check_pipeline_health()
        
        print(f"\n📊 Pipeline Health Status: {health_status['overall_health']}")
        print(f"Timestamp: {health_status['timestamp']}")
        
        print(f"\nChecks:")
        for check_name, check_result in health_status['checks'].items():
            status_icon = {"HEALTHY": "✅", "WARNING": "⚠️", "CRITICAL": "❌", "ERROR": "💥", "UNKNOWN": "❓"}.get(check_result['status'], "❓")
            print(f"  {status_icon} {check_name}: {check_result['message']}")
        
        if health_status['alerts']:
            print(f"\nAlerts:")
            for alert in health_status['alerts']:
                print(f"  🚨 {alert}")
        
        if health_status['recommendations']:
            print(f"\nRecommendations:")
            for rec in health_status['recommendations']:
                print(f"  💡 {rec}")
    
    elif args.command == 'monitor':
        # Continuous monitoring
        monitor.run_continuous_monitoring(args.interval)

if __name__ == "__main__":
    main()
